import {
  SearchByKeywordSuggestions,
  SearchByKeywordSuggestionsProps,
} from '@/components/SearchByKeywords/SearchByKeywordSuggestions'
import { MultiSelectV2Props } from '@/components/ui/Select/MultiSelectV2'

import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { useGetInfiniteKeywordsV2 } from '@/hooks/query/keyword/useGetInfiniteKeywordsV2'
import { useTranslations } from 'next-intl'
import { useCallback, useMemo, useState } from 'react'

interface PostSearchBoxProps extends Omit<SearchByKeywordSuggestionsProps, 'selectProps'> {
  onKeywordsChange?: (keywords: string[]) => void
  selectProps?: Omit<MultiSelectV2Props<Record<string, any>>, 'value' | 'onValueChange' | 'options'>
  showAllLanguages?: boolean
}

// const keywordCategoriesFilter = [
//   KeywordCategoryEnum.BODY_PART,
//   KeywordCategoryEnum.MEDICINE,
//   KeywordCategoryEnum.SYMPTOM_OR_DISEASE,
//   KeywordCategoryEnum.ALLERGY_TYPE,
//   KeywordCategoryEnum.MEDICINE_TYPE,
// ]

export const PostSearchBox = ({
  onKeywordsChange,
  initialKeywords,
  selectProps,

  ...props
}: PostSearchBoxProps) => {
  const t = useTranslations()
  const { primaryLanguage } = useAppLanguage()
  const [searchValue, setSearchValue] = useState<string>('')

  const { keywords, fetchNextPage, hasNextPage, isGetKeywordsLoading, isFetchingNextPage } =
    useGetInfiniteKeywordsV2({
      overrideKey: ['list-select-keywords', searchValue],
      params: {
        limit: 15,
        locale: 'all',
        searchValue: searchValue || '',
        // categories: keywordCategoriesFilter,
      },
      options: {
        cache: 'default',
        next: {
          revalidate: Infinity,
        },
      },
      config: {
        staleTime: Infinity,
      },
    })

  const flatItems = useMemo(() => {
    return keywords?.pages.flatMap((page) => page?.docs).filter((item) => !!item) || []
  }, [keywords?.pages])

  // Memoize the handler for keywords change
  const handleKeywordsChange = useCallback(
    (k: string[]) => {
      onKeywordsChange?.(k)
    },
    [onKeywordsChange],
  )

  // Memoize the external search handler
  const handleExternalSearch = useCallback((v: string) => {
    setSearchValue(v)
  }, [])

  return (
    <SearchByKeywordSuggestions
      onKeywordsChange={handleKeywordsChange}
      selectProps={{
        options: flatItems,
        displayKey: `name.${primaryLanguage}`,
        valueKey: `name.${primaryLanguage}`,
        fetchNextPage,
        hasNextPage: !!hasNextPage,
        isFetchingNextPage,
        externalSearch: handleExternalSearch,
        isSearching: isGetKeywordsLoading,
        modalPopover: true,
        showBadges: false,
        popoverProps: {
          className: '!z-[1000] pointer-events-auto',
        },
        placeholder: t('MES-67'),
        showArrowIcon: false,
        showSearchIcon: true,
        selectTriggerClassName: 'h-[44px]',
        allowCustomValues: true,
        alwaysShowCustomValue: true,
        renderCustomValue: (searchTerm) => (
          <div className="flex cursor-pointer items-center gap-x-1 bg-primary-50 px-3 py-2">
            <span className="typo-link-3 text-primary">{t('MES-143')}: </span>
            <span className="flex items-center">
              {`"`}
              <span className="typo-body-7 line-clamp-1 block min-w-0 max-w-[200px] truncate">
                {searchTerm}
              </span>
              {`"`}
            </span>
          </div>
        ),
        limit: 5,

        ...selectProps,
      }}
      initialKeywords={initialKeywords}
      isShowKeywordInTop={true}
      {...props}
    />
  )
}
