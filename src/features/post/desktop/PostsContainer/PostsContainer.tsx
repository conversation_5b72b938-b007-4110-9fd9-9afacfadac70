import React from 'react'
import { PostsPanel } from '../PostsPanel/PostsPanel'
import { PostsList } from '../PostsList/PostsList'

interface PostsContainerProps {
  params?: {
    [key: string]: string | undefined
  }
}
export const PostsContainer: React.FC<PostsContainerProps> = ({ params }) => {
  return (
    <>
      <PostsPanel></PostsPanel>
      <PostsList params={params}></PostsList>
    </>
  )
}
