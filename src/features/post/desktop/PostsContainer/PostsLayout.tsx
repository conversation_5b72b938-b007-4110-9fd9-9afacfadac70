import React from 'react'
import { PostsContainer } from './PostsContainer'
import { useTranslations } from 'next-intl'
import PostsSearch from '../PostsSearch/PostsSearch'

interface PostsContainerProps {
  params?: {
    [key: string]: string | undefined
  }
}
export const PostsLayoutContainer: React.FC<PostsContainerProps> = ({ params }) => {
  const t = useTranslations()
  return (
    <div className="bg-custom-background-hover px-16 py-6">
      <div className="rounded-3xl bg-white p-4">
        <div className="flex items-center justify-between gap-2">
          <div className="typo-heading-7 text-primary-500">{t('MES-19')}</div>
          <PostsSearch />
        </div>
        <PostsContainer params={params} />
      </div>
    </div>
  )
}
